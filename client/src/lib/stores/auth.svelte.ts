/**
 * Authentication State using Svelte 5 Runes and Context Pattern
 *
 * This module provides reactive authentication state management using the
 * reactive class pattern with Svelte 5 runes. It handles user authentication
 * status, login/logout operations, and provides context for the entire app.
 *
 * Key features:
 * - Cookie-based authentication (no localStorage)
 * - Server-side state initialization
 * - Reactive class pattern with $state runes
 * - Context pattern for sharing state across components
 */

import { getContext, setContext } from 'svelte';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { APP_CONFIG } from '$lib/config/env';
import type { User, LoginCredentials } from '$types/auth';

/**
 * Authentication state interface
 */
interface AuthState {
	isAuthenticated: boolean;
	isLoading: boolean;
	user: User | null;
	error: string | null;
	login: (credentials: LoginCredentials) => Promise<boolean>;
	clearError: () => void;
}

