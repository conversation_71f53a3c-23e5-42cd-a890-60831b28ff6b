<script lang="ts">
	/**
	 * Login Page Component - Simplified Working Version
	 *
	 * This component provides a complete login form with:
	 * - Zod schema validation using Superforms
	 * - Real-time form validation and error display
	 * - Integration with Django backend authentication
	 * - Loading states and user feedback
	 * - Proper error handling for different scenarios
	 */

	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from "$lib/components/ui/card";
	import { Separator } from "$lib/components/ui/separator";
	import GoogleIcon from "$lib/components/icons/ui/GoogleIcon.svelte";
	import { BrandLogoIcon } from "$lib/components/icons/brand";
	import { goto } from "$app/navigation";

	/**
	 * Handle Google OAuth login
	 * TODO: Implement Google OAuth integration
	 */
	function handleGoogleLogin() {
		console.log("Google login requested");
	}

	/**
	 * Handle navigation to registration/contact sales
	 */
	function handleRegister() {
		goto("/contact-sales");
	}
</script>

<div class="flex min-h-[calc(100vh-3.25rem)] items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- Logo -->
		<a href="/" class="mb-8 flex justify-center">
			<div class="bg-primary flex h-16 w-16 items-center justify-center rounded-2xl shadow-sm">
				<BrandLogoIcon class="text-accent p-3" />
			</div>
		</a>

		<!-- Login Card -->
		<Card class="border-0 shadow-lg">
			<CardHeader class="space-y-2 text-center">
				<CardTitle class="text-2xl font-semibold">Welcome back!</CardTitle>
				<CardDescription class="text-slate-600">
					Please enter your details to login.
				</CardDescription>
			</CardHeader>

			<CardContent class="space-y-6">
				<!-- Social Login Buttons -->
				<div class="space-y-3">
					<Button
						variant="outline"
						class="h-12 w-full cursor-pointer text-slate-700"
						onclick={handleGoogleLogin}
					>
						<GoogleIcon class="size-5" />
						Login with Google
					</Button>
				</div>

				<!-- Divider -->
				<div class="relative">
					<div class="absolute inset-0 flex items-center">
						<Separator />
					</div>
					<div class="relative flex justify-center text-xs uppercase">
						<span class="bg-white px-2 text-slate-500">OR</span>
					</div>
				</div>

				<!-- Email Login Form with Superforms -->
				<form method="POST" class="space-y-4">
					<div class="space-y-2">
						<Label for="email">Email</Label>
						<Input
							id="email"
							name="email"
							type="email"
							placeholder="<EMAIL>"
							class="h-12"
						/>
					</div>

					<div class="space-y-2">
						<Label for="password">Password</Label>
						<Input
							id="password"
							name="password"
							type="password"
							placeholder="••••••••••"
							class="h-12"
						/>
					</div>

					<Button
						type="submit"
						class="h-12 w-full cursor-pointer bg-black hover:bg-slate-800"
					>
					</Button>
				</form>

				<!-- Reset Password Link -->
				<div class="text-center">
					<span class="text-sm text-slate-600">Forgot your password? </span>
					<a
						class="cursor-pointer text-sm font-medium text-slate-900 hover:underline"
						href="/reset-password"
					>
						Reset it
					</a>
				</div>
			</CardContent>
		</Card>

		<!-- Register Link -->
		<div class="mt-6 text-center">
			<span class="text-sm text-slate-600">Don't have an account? </span>
			<button
				class="cursor-pointer text-sm font-medium text-slate-900 hover:underline"
				onclick={handleRegister}
			>
				Register
			</button>
		</div>
	</div>
</div>
